const { momentNow } = require("../helpers");
const { FirestoreRef, CONSTANTS, COLLECTIONS, moment } = require("../init");
const { MOMENT_ISO } = CONSTANTS;
const axios = require("axios");
const {
  getLeadById,
  getSegmentationsByIds,
  getInstanceById,
} = require("../post");
const { getIgIdByInstance } = require("../utils/instacesUtils");
const { getLeadsIdsFromSegmentation } = require("../leads");
const { shotxOrganizeMessages } = require("../shotxOrganize");
const { shotxSendMessages } = require("../shotxSendMessages");
const { instanceId } = require("firebase-admin");
const shotxCron = async () => {
  let now = momentNow().add(5, "minutes").format(MOMENT_ISO);
  console.log("SHOTXCRON > START");

  const fetchSchedule = async () => {
    let scheduled = [];

    await FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
      .where("status", "==", "published")
      .where("scheduled_date", "<=", now)
      .get()
      .then((snapshot) => {
        return snapshot.docs.map((doc) => {
          scheduled.push(doc.data());
          doc.ref.update({
            modified_date: momentNow().format(MOMENT_ISO),
            status: "sent",
          });
          return scheduled;
        });
      });
    console.log("SHOTXCRON > FETCHED", scheduled.length);
    return scheduled;
  };

  return fetchSchedule().then(async (schedules) => {
    if (schedules && schedules.length > 0) {
      let preparedSchedules = [];
      try {
        for (const appointment of schedules) {
          console.log("SHOTXCRON > PREPARE > APP", appointment);
          const prepared = await prepareLeadsOnSchedules(appointment);
          console.log("SHOTXCRON > PREPARE > PREPARED", prepared);
          FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
            .doc(appointment.ID)
            .update({ segmentationsIds: prepared.segmentationsIds });
          console.log("SHOTXCRON > PREPARE > PREPARED SCHEDULE", prepared);
          preparedSchedules = [
            ...preparedSchedules,
            ...prepared.preparedSchedules,
          ];
        }
      } catch (error) {
        console.log("SHOTXCRON > PREPARE > ERROR", error);
      }

      shotxOrganizeMessages(preparedSchedules);
    }
    return null;
  });
};

const ordeneInstancesOrMessages = (
  type,
  instancesOrMessages,
  qtdLeads,
  random = false
) => {
  // Validações necessárias
  if (type !== "instances" && type !== "messages") {
    throw new Error("Parâmetro 'type' deve ser 'instances' ou 'messages'");
  }

  if (!Array.isArray(instancesOrMessages)) {
    throw new Error("Parâmetro 'instancesOrMessages' deve ser um array válido");
  }

  if (!Number.isInteger(qtdLeads) || qtdLeads <= 0) {
    throw new Error("Parâmetro 'qtdLeads' deve ser um número inteiro positivo");
  }

  // Se o array está vazio, retorna array vazio
  if (instancesOrMessages.length === 0) {
    console.log("SHOTXCRON > PREPARE > EMPTY ARRAY");
    return [];
  }

  let workingArray = [...instancesOrMessages]; // Cópia para não modificar o original

  // Se random = true, embaralhar o array
  if (random) {
    // Algoritmo Fisher-Yates para embaralhamento
    for (let i = workingArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [workingArray[i], workingArray[j]] = [workingArray[j], workingArray[i]];
    }
  }

  const result = [];

  // Se precisamos de mais elementos do que temos, repetir o array
  while (result.length < qtdLeads) {
    const remainingNeeded = qtdLeads - result.length;
    const elementsToTake = Math.min(remainingNeeded, workingArray.length);

    // Adicionar elementos do array (objetos com id e accountId)
    for (let i = 0; i < elementsToTake; i++) {
      const element = workingArray[i];
      // Extrair ID do elemento (tanto instances quanto messages usam 'id' ou 'ID')
      const id = element.id || element.ID;
      // Criar objeto com id e accountId
      result.push({
        id: id,
        accountId: element.accountId,
      });
    }

    // Se ainda precisamos de mais elementos, embaralhar novamente se random = true
    if (result.length < qtdLeads && random) {
      for (let i = workingArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [workingArray[i], workingArray[j]] = [workingArray[j], workingArray[i]];
      }
    }
  }

  return result;
};

const prepareLeadsOnSchedules = async (schedule) => {
  const leads = schedule.contacts || [];
  console.log("SHOTXCRON > PREPARE > LEADS", leads);

  if ((!leads || leads.length === 0) && !schedule.segmentations) {
    console.log("SHOTXCRON > PREPARE > NO LEADS", schedule);
    return null;
  }

  const preparedSchedules = [];
  const contacts = [];
  const segmentationsIds = [];
  if (schedule.hasOwnProperty("segmentations")) {
    const segmentations = await getSegmentationsByIds(schedule.segmentations);

    for (const segmentation of Object.values(segmentations)) {
      let contactIds;

      if (segmentation.config.dinamic) {
        console.log("SHOTXCRON > PREPARE > SEGMENTATION DINAMIC", segmentation);
        contactIds = await getLeadsIdsFromSegmentation(segmentation);
        segmentationsIds.push(...contactIds.data.contactIds);
        leads.push(...contactIds.data.contactIds);
      } else {
        console.log("SHOTXCRON > PREPARE > SEGMENTATION STATIC", segmentation);
        contactIds = segmentation.leads;
        console.log(
          "SHOTXCRON > PREPARE > SEGMENTATION STATIC",
          segmentation.leads
        );
        leads.push(...contactIds);
        segmentationsIds.push(...contactIds);
      }
      console.log(
        "SHOTXCRON > PREPARE > LEADS IDS FROM SEGMENTATION",
        contactIds
      );
    }
  }

  // console.log("SHOTXCRON > PREPARE > segmentatios", segmentatios);

  // Declarar array no escopo acessível com valor padrão
  let instancesOrdered = null;
  if (schedule.multipleInstanceMode) {
    instancesOrdered = ordeneInstancesOrMessages(
      "instances",
      schedule.instance,
      leads.length,
      false
    );
  }

  let index = 0;
  for (const leadId of leads) {
    console.log("SHOTXCRON > PREPARE > CONTACTS", contacts);
    console.log("SHOTXCRON > PREPARE > LEADS", leads);
    console.log(
      "SHOTXCRON > PREPARE > instanceORDEred",
      instancesOrdered[index]
    );
    const lead = (await getLeadById(leadId)) || {};
    const instance = (await getInstanceById(instancesOrdered[index].id)) || {};
    if (lead) {
      let contactRemoteId;
      let contactName;
      let accountIdPrepared =
        schedule.multipleInstanceMode && instance
          ? instance.accountId
          : schedule.instance.accountId;

      let instanceIdPrepared =
        schedule.multipleInstanceMode && instance
          ? instance.id
          : schedule.instance.id;

      console.log("SHOTXCRON > PREPARE > INSIDE IF > LEAD ", lead);
      console.log("SHOTXCRON > PREPARE > INSIDE IF > INSTANCE ", instance);
      console.log(
        "SHOTXCRON > PREPARE > INSIDE IF > ACCOUT ",
        accountIdPrepared
      );
      console.log(
        "SHOTXCRON > PREPARE > INSIDE IF > INSTANCEprepa ",
        instanceIdPrepared
      );
      switch (schedule.instance.platform) {
        case "Whatsapp":
          contactName = lead.displayName;
          contactRemoteId = lead.mobileCC + lead.mobile;
          contacts.push({
            id: leadId,
            contactName,
            contactRemoteId,
            instanceId: instanceIdPrepared,

            accountId: accountIdPrepared,
          });
          index++;
          break;
        case "Instagram":
          contactName = lead.displayName;
          contactRemoteId = getIgIdByInstance(lead, schedule.instance);
          contacts.push({
            id: leadId,
            contactName,
            contactRemoteId,
            instanceId: instanceIdPrepared,

            accountId: accountIdPrepared,
          });
          index++;
          break;
        default:
          break;
      }
    } else {
      console.log("SHOTXCRON > PREPARE >LEAD NOT FOUND", leadId);
    }
  }
  const schedulePrepared = {
    ...schedule,
    contacts,
  };

  preparedSchedules.push(schedulePrepared);
  return {
    preparedSchedules,
    segmentationsIds,
  };
};

module.exports = {
  shotxCron,
};
